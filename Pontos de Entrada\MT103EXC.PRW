#include 'totvs.ch'


//-------------------------------------------------------------------
/*/{Protheus.doc} MT103EXC
Ponto de Entrada utilizado na exclusão do Documento de Entrada
<AUTHOR>
@since   09/06/2020
@version 1.0
/*/
//-------------------------------------------------------------------

User Function MT103EXC
	Local lRet       := .T.
	Local lVldExcl   := GetMv("TI_V103EXC",,.T.)
	Local lDespDif 	 := GetMv("TI_DESDIFE",,.F.)
	Local oTIContab
// Verifica se deve validar a exclusão do Documento de Entrada e se trata-se de uma NF de devolução
	If lVldExcl .And. SF1->F1_TIPO == 'D'
		If GetRemoteType()==-1
			PMT103EXC()
		Else
			FwMsgRun(, { || PMT103EXC() }, "Saldo contrato", "Recalculando Saldos do Contrato" )
		EndIf
	Endif
	if (lDespDif)
		oTIContab:= TICONTABIL():New()
		oTIContab:deleteDespesaDiferida(SF1->F1_FILIAL,SF1->F1_TIPO,SF1->F1_DOC,SF1->F1_SERIE,SF1->F1_FORNECE,SF1->F1_LOJA)
		FREEOBJ( oTIContab )
	EndIF
Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} PMT103EXC
Processamento do Ponto de Entrada utilizado na exclusão do Documento de Entrada, para exibir msg de processando
<AUTHOR> Donizete
@since   09/06/2020
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function PMT103EXC
	Local nX
	Local nPosNfOri  := aScan(aHeader,{|x| AllTrim(x[2])=="D1_NFORI"})
	Local nPosSeriOri:= aScan(aHeader,{|x| AllTrim(x[2])=="D1_SERIORI"})
	Local aArea      := GetArea()
	Local aAreaSC6   := SC6->(GetArea())
	Local aContrProc := {}
	Local cAliasTrb  := GetNextAlias()

//Verifica vinculo com Pedidos de Venda //
	For nX := 1 to len(aCols)
		// Posiciona no Pedido de Venda
		BeginSql Alias cAliasTrb
        SELECT C6_CONTRT
            FROM %table:SC6%
            WHERE C6_FILIAL = %xFilial:SC6%
                AND C6_NOTA = %exp:aCols[nX,nPosNfOri]%
                AND C6_SERIE = %exp:aCols[nX,nPosSeriOri]%
                AND C6_CONTRT <> ' '
                AND %notDel%   
		EndSql
		If !(cAliasTrb)->(Eof())
			// Se o contrato ainda não foi processado, processa atualização do saldo.
			If Empty(Ascan(aContrProc, (cAliasTrb)->C6_CONTRT))
				//StaticCall(TGCVJ030,ProcSaldoCn9,(cAliasTrb)->C6_CONTRT, .T., .T.)
				U_ProcSaldoCn9((cAliasTrb)->C6_CONTRT, .T., .T.)
				// Guarda os contratos já processados
				Aadd(aContrProc, (cAliasTrb)->C6_CONTRT)
			EndIf
		EndIf
		(cAliasTrb)->(DbCloseArea())
	Next nX

	SC6->(RestArea(aAreaSC6))
	RestArea(aArea)
	aArea := aSize(aArea,0)
	aAreaSC6 := aSize(aAreaSC6,0)
	aContrProc := aSize(aContrProc,0)

Return
