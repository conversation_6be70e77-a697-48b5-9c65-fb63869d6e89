#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"

Static POS_CCUSTO := 1
Static POS_ITEMCT := 2
Static POS_CLASSE := 3
Static POS_CONTAC := 4
Static LEN_ENTCTB := 4

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³ MT103INF ºAutor  ³ TOTVS              º Data ³ 15/09/2008  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Ponto de entrada na vinculacao da NF de Origem a NF de     º±±
±±º          ³ devolucao. Incluindo tratamento para preenchimento das     º±±
±±º          ³ entidades contabeis Centro Custo, Item Ctb e Classe Vlr.   º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ Protheus Corporativo TOTVS                                 º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function MT103INF()
Local aArea     := GetArea()
Local aAreaSBM	:= SBM->(GetArea())
Local aAreaSB1	:= SB1->(GetArea())
Local aAreaCTT	:= CTT->(GetArea())
Local nItem     := PARAMIXB[1]
Local nPosCC    := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_CC"})
Local nPosItCt  := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_ITEMCTA"})
Local nPosClVl  := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_CLVL"})
Local nPosCont  := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_CONTA"})
Local nPosSerOr := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_SERIORI"})
Local nPosProd  := aScan(aHeader,{|x| AllTrim(x[2]) == "D1_COD"})
Local lAtiva    := GetMv("MV_MT103INF",, .F.)
Local cCCRet    := ""
Local cItCtRet  := ""
Local cClVlRet  := ""
Local _cGrp     := SD2->D2_GRUPO
Local _cCodPro  := aCols[nItem,nPosProd]
Local _cSerOri  := aCols[nItem,nPosSerOr]
Local cCus      := ""
Local __cCodPrj := ""
Local aRetCtb   := {"","","",""} 
Local cUnSrv    := ""
Local cDRE		:= ""
Local nPosQuant	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_QUANT"}) 
Local nNfOri	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_NFORI"})
Local lMT103Ncc := GetMv("TI_VLDPSAZ",, .T.)

If nPosCC <> 0

	//Enderecamento padrao
	aCols[nItem,nPosCC] := SD2->D2_CCUSTO
	cCus := SD2->D2_CCUSTO

	//Enderecamento para Receita recorrente
	If !Empty(cCus)//u_AtrRecSBM( SD2->D2_CLIENTE, SD2->D2_LOJA, SD2->D2_COD, SC5->C5_NUM, aRetCtb )
	
		//If !Empty(aRetCtb[POS_CCUSTO])

			//aRetCtb[POS_CCUSTO] := U_VLDCC(aRetCtb[POS_CCUSTO])
			aCols[nItem,nPosCC] := U_VLDCC(cCus) //aRetCtb[POS_CCUSTO]
			//cCus := aRetCtb[POS_CCUSTO]
			
		//EndIf

	//Enderecamento para Receitas de Projeto
	Else

		SC6->( DbSetOrder(1))

		If SC6->( DbSeek( xFilial("SC6") + SD2->D2_PEDIDO + SD2->D2_ITEMPV ) ) .And. u_AtrRecPE5( SC6->(Recno()), SF2->(Recno()), SD2->(Recno()), aRetCtb)
		
			If !Empty(aRetCtb[POS_CCUSTO])
			
				aRetCtb[POS_CCUSTO] := U_VLDCC(aRetCtb[POS_CCUSTO])
				aCols[nItem,nPosCC] := aRetCtb[POS_CCUSTO]
				cCus := aRetCtb[POS_CCUSTO]
				
			EndIf

		EndIf
		If Empty(cCus)
			aCols[nItem,nPosCC] := GetMv("MV_#CCMAT" ,,"700999999")
		EndIf 
	EndIf

EndIf


If nPosItCt <> 0
	aCols[nItem,nPosItCt] := SD2->D2_ITEMCC
	cItem:= SD2->D2_ITEMCC
EndIf

If nPosCont <> 0

	SB1->( DbSetOrder(1) )
	If SB1->( DbSeek( xFilial("SB1") + SD2->D2_COD ) )
	
		SBM->( DbSetOrder(1) )
		If SBM->( DbSeek( xFilial("SBM") + SB1->B1_GRUPO ) )
			
			If GetAdvFVal("SA1","A1_PAIS",xFilial("SA1") + SD2->(D2_CLIENTE+D2_LOJA),1) != "105" // -- NCC PARA CLIENTE FORA DO BRASIL
				aCols[nItem,nPosCont] := SBM->BM_XEXPD
				cCtb := SBM->BM_XEXPD				
			Else
				If left(SBM->BM_XCONTAD,4) == "4000"
					
					cDRE := Alltrim( Posicione("CTT",1, xFilial("CTT") + cCus, "CTT_XDRE"))

					If !Empty(cDRE)
						cCtb := Stuff( SBM->BM_XCONTAD, 2, Len(cDRE), cDRE )
					Else
						cCtb := SBM->BM_XCONTAD
					EndIf
				
				Else

					cCtb := SBM->BM_XCONTAD
						
				EndIf

				aCols[nItem,nPosCont] := cCtb
							
			EndIf	
			
		EndIf
	
	EndIf

	If Empty(aCols[nItem,nPosCont])
		aCols[nItem,nPosCont] := SD2->D2_CONTA
		cCtb := SD2->D2_CONTA
	EndIf
	
EndIf

If nPosClVl != 0
	aCols[nItem,nPosClVl] := SD2->D2_CLVL
EndIf

if lMT103Ncc .and. !empty(aCols[1,nNfOri]) .AND. !EMPTY(SF1->F1_XIDPSA) .AND. aCols[nItem,nPosQuant] == 0
	aCols[nItem,nPosQuant] = 1
	A103Trigger("D1_QUANT")

	If ExistTrigger('D1_QUANT') 
        RunTrigger(2,nItem,nil,,'D1_QUANT')
        MaColsToFIs(aHeader,aCols,nItem,"MT100") //Grava o gatilho no campo caso possua a função MaFisRef 
    EndIf
endif

SB1->(RestArea(aAreaSB1))
SBM->(RestArea(aAreaSBM))
CTT->(RestArea(aAreaCTT))
RestArea(aArea)

Return Nil

//==================================================================
/*/{Protheus.doc} PesqVend
Pesquisa dados de classe de valor e centro de custo do vendedor
<AUTHOR>
@since 01/01/2014
@version 1.0
@param cVend, character, Codigo de vendedor SA3
@return aDados Dados de classe de valor e centro de custo do vendedor
/*/
//==================================================================
Static Function PesqVend(cVend)

Local cSuper	:= ""
Local cRet		:= ""
Local cCC		:= ""
Local aDados	:= {}
Local aAreaTmp	:= GetArea()
Local lNewCCr	:= GetMv("MV_#NEWCC",,.F.)


///-- Pesquisa Vendedor Principal da Proposta
DbSelectArea("SA3")
DbSetOrder(1)
If !Empty(Alltrim(cVend))
	If DbSeek(xFilial("SA3")+cVend)
		
		///-- Alimenta dados de Classe de Valor e Centro de custo do Vendedor
		cRet := Alltrim(SA3->A3_XCLVL)
		cCC  := Alltrim(SA3->A3_XCC)
		
		If !lNewCCr  //Opcao desabilitada para Novo modelo de gestao - valido a partir de 01/01/2014 nao deve-se trocar digitos do novo centro de custos
			///-- Trocar ultimo digito do centro de custo para a receita cair no GAR.
			If Len(cCC) == 8
				If Right(cCC,1) <> '0'
					cCC	:=	Substr(cCC,1,Len(cCC)-1) + '0'
				Endif
			EndIf
		Endif
		
		cSuper	:= SA3->A3_SUPER
		///-- Pesquisa pelo superior do Vendedor para verificar se eh Franquia Externa.
		If DbSeek(xFilial("SA3")+cSuper)
			If !lNewCCr
				cEntdSup := Alltrim(SA3->A3_XCLVL)
				cEntdFrq := "930000"
			Else
				cEntdSup := Alltrim(SA3->A3_XCC)
				cEntdFrq := "110999999"
			EndIf
			If cEntdSup == cEntdFrq
				cRet := Alltrim(SA3->A3_XCLVL)
				cCC  := Alltrim(SA3->A3_XCC)
			Endif
		Endif
	Endif
Endif


Aadd(aDados,{cRet,cCC})

RestArea(aAreaTmp)

Return(aDados)

//---------------------------------------------------------------------------
Static Function RetUnid(cVend)
Local cRet    := ""
Local aArea   := GetArea()
Local aAreaSA3	:= SA3->(GetArea())
Local cUnVend := ""
Local aUnVend	:= {}

Default cVend := ""

If Empty(cVend)
	Return cRet
EndIf

SA3->( DbSetOrder(1) )
If SA3->( DbSeek(xFilial("SA3") + cVend ) )
	
	cCodUsro := GetAdvFVal("AZS","AZS_CODUSR",xFilial("AZS")+cVend,4,"")
	
	If !Empty(cCodUsro)
	
		aUnVend := U_TCRMXEqp(cVend)
		
		If !Empty(aUnVend[2])
			cUnVend := aUnVend[2]
		EndIf
	
	EndIf
	
	If Empty(cUnVend)
	
		cUnVend := SA3->A3_UNIDAD
	
	EndIf

Endif

SA3->(RestArea(aAreaSA3))
RestArea(aArea)
Return cRet
