#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"

/*
Rateio SDE na geração da Nota fiscal de entrada 
Autor: <PERSON>
*/

User Function MT103RTE()
    Local aArea         := GetArea()
    
    Local aHeaderSDE    := aClone(PARAMIXB[1])
    Local aColsSDE      := aClone(PARAMIXB[2])
    //Local nPosColSD1    := PARAMIXB[3]

    If INCLUI .and. SF1->F1_TIPO == 'D' 
        Mensalizado(aHeaderSDE, aColsSDE)
    EndIf     

    
    RestArea(aArea)

return {aHeaderSDE, aColsSDE}

Static Function Mensalizado(aHeaderSDE, aColsSDE)
    Local aAreSD2      := SD2->(GetArea())
    Local cChavSD2     := SD1->(D1_FILIAL + D1_NFORI + D1_SERIORI + D1_FORNECE + D1_LOJA + D1_ITEMORI)

    SD2->(Db<PERSON><PERSON>r<PERSON>ick<PERSON>ame("SD2AVP")) //D2_FILIAL+D2_DOC+D2_SERIE+D2_CLIENTE+D2_LOJA+D2_ITEM

    If SD2->(DbSeek(cChavSD2))
        If SD2->D2_XMENSAL == "1"
            RateioSDE(aHeaderSDE, aColsSDE)
            SD1->D1_RATEIO := "1"
        EndIf
    EndIf

    SD2->(RestArea(aAreSD2))

Return

Static Function RateioSDE(aHeaderSDE, aColsSDE)
    Local oSaastizado := Tgcvxc33():New()

    oSaastizado:GetProposNF() 
    oSaastizado:Load()
    oSaastizado:Calcula() 
    oSaastizado:GravaPWH()
    oSaastizado:MontaAcols(aHeaderSDE, aColsSDE)
    oSaastizado:Destroy()

Return
