#include 'protheus.ch'
#include 'parmtype.ch'
#Include 'TopConn.ch'

//-------------------------------------------------------------------
/*/{Protheus.doc} MT103FIM
Ponto de Entrada no fim da entrada da NF para verificar se é necessário
atualizar o status de comissão no Portal BemaCash
<AUTHOR> <PERSON>
@since   07-08-2017
@version 1.0
/*/
//-------------------------------------------------------------------

User Function MT103FIM()

	Local nOpcao    := PARAMIXB[1]
	Local nConfirma := PARAMIXB[2]

	Local lExecInt  := GetMv("TI_INTBCH", , .T.) //Integração BemaCash
	Local cProdBema := GetMv("TI_PRDBCH", , "T") //Integração BemaCash

	Local aArea     := GetArea()
	Local aSF1Area  := SF1->(GetArea())
	Local aSD1Area  := SD1->(GetArea())
	Local aSC7Area  := SC7->(GetArea())
	Local aSE3Area  := SE3->(GetArea())
	Local aZPXArea  := ZPX->(GetArea())
	

	Local cCodInt 	:= GetMv("CMS_CODINT",,"000002")//Codigo empresa x sistema integrador com o Protheus PN2
	Local cChave    := SF1->F1_FILIAL + SF1->F1_DOC + SF1->F1_SERIE + SF1->F1_FORNECE + SF1->F1_LOJA
	
	Local cProcesso		:= "000011"
	Local cToKenExt		:= ""

	Local cJson			:= ""
	Local cRetWsPad		:= ""
	Local cRetWsErro	:= ""
	Local aRecPN1		:= {}

	Local cQrySE3   := ""
	Local cAlsSE3   := GetNextAlias()

	Local cStatus   := "8"

	Local cQryZPX
	Local cAlsZPX

	Local cMsgVld		:= ""
	Local lHbIntPSA		:= getMv('TI_#HABPSA',,.F.)
	Local aDadosPSA 	:= {}
	Local aRet          := {}
	Local cAliasQry     := ""
	Local cNumMed       := ""
	Local cNumCompo     := ""

	Local cBemaFil      := GetMv("TI_FILBEM",,"02501000100/02501000200/02501002000/02501002200/0251001500/02501000700/02501000500/01903000100") //Integração Bematech
	Local cUrlBema      := GetMv("TI_URLBEM",,"http://200.196.90.152:8182/Servicos.svc")
	Local cMetodBema    := GetMv("TI_MTDBEM",,"/AlterarStatusLoteComissao")
	Local cTpPedido     := GetMv("TI_#TPPED",,"8")
	Local aHeader	    := {}
	Local cPedido       := ""
	Local cDocS         := SD1->D1_NFORI
	Local cSerieS       := SD1->D1_SERIORI
	Local lDespDif 		:= GetMv("TI_DESDIF",,.F.)
	Local oTIContab
	Local lDeduz        := .F.
	Local lDifere       := .T.
	Local aAreaPb1		:= PB1->(GetArea())
	Local cSeek

//Integração Bematech.

	//Integração PSA Nota de Servico recorrente NCC
	tlpp.call("U_GCPSAIntegraNCC", SF1->F1_FILIAL, SF1->F1_DOC, SF1->F1_SERIE, nOpcao, nConfirma)
	//Integração PSA Nota de Servico recorrente Excluisao NCC
	tlpp.call("U_GCPSAIntegraExcNCC", SF1->F1_FILIAL, SF1->F1_DOC, SF1->F1_SERIE, nOpcao, nConfirma)

	//Integração Bematech.
	If SF1->F1_FILIAL $ cBemaFil .And. nOpcao == 3 .And. nConfirma == 1

		SD1->(dbSetOrder(1))

		If SD1->(dbSeek(cChave))

			DbSelectArea("SC7")
			SC7->(dbSetOrder(4))

			oRest := FWREST():New(cUrlBema)
			oRest:SetPath(cMetodBema)

			//----------------------------------------------------
			// tratamento para o Header da API
			//----------------------------------------------------
			Aadd( aHeader, 'Content-Type: application/json' )

			While SD1->(!Eof()) .And. ( cChave == SD1->D1_FILIAL + SD1->D1_DOC + SD1->D1_SERIE + SD1->D1_FORNECE + SD1->D1_LOJA )

				If SC7->(dbSeek(SD1->D1_FILIAL + SD1->D1_COD + SD1->D1_PEDIDO + SD1->D1_ITEMPC))

					If !Empty(SD1->D1_PEDIDO) .And. !Empty(SD1->D1_ITEMPC) .And. SD1->D1_PEDIDO != cPedido .And. AllTrim(SC7->C7_TPPED) == Alltrim(cTpPedido)

						cPedido := SD1->D1_PEDIDO

						cJSON += '{'
						cJSON += '"PedCompProtheus":"' + SD1->D1_PEDIDO + '",'
						cJSON += '"Status":8'
						cJSON += '},'

					EndIf

				EndIf

				SD1->(DbSkip())

			End

			If !Empty(cJSON)

				cJSON := "[" + Left(cJSON, RAT(",", cJSON) - 1) + "]"

				oRest:SetPostParams( cJson )

				lRet := oRest:Post(aHeader)

				//Caso o processamento falhe gravo a PNL para depois o Schedule processar novamente
				If !lRet

					DbSelectArea("PNL")

					PNL->(RecLock("PNL",.T.))

					PNL->PNL_ALIAS  := "SD1"
					PNL->PNL_CHVUNI := SD1->(D1_FILIAL+D1_DOC+D1_SERIE+D1_FORNECE+D1_LOJA)
					PNL->PNL_JSON   := cJSON
					PNL->PNL_STATUS := "1"

					PNL->(MsUnlock())

				EndIf

			EndIf

		EndIf

	EndIf


	If lExecInt

		If nOpcao == 3 .And. nConfirma == 1
			//Se for filial BEMACASH inicia o processo
			SD1->(dbSetOrder(1))
			If SD1->(dbSeek(cChave))
				While SD1->(!Eof()) .And. ( cChave == SD1->D1_FILIAL + SD1->D1_DOC + SD1->D1_SERIE + SD1->D1_FORNECE + SD1->D1_LOJA )
					If !Empty(SD1->D1_PEDIDO) .And. !Empty(SD1->D1_ITEMPC)
						dbSelectArea("SC7")
						SC7->(dbSetOrder(4))
						If SC7->(dbSeek(SD1->D1_FILIAL + SD1->D1_COD + SD1->D1_PEDIDO + SD1->D1_ITEMPC))

							If AllTrim(SC7->C7_XTTIPCO) == "6" //Pedido de Comissões

								//Posicionar na SE3
								cQrySE3 := "SELECT SE3.E3_NUM, SE3.E3_PREFIXO, SE3.E3_PARCELA, SE3.E3_TIPO, SE3.E3_EMISSAO  "
								cQrySE3 += "FROM " + RetSqlName("SE3") + " SE3 "
								cQrySE3 += "WHERE "
								cQrySE3 += "SE3.E3_PEDIDO  = '" + SC7->C7_NUM + "' AND "
								cQrySE3 += "SE3.D_E_L_E_T_ = '' "
								cQrySE3 += "GROUP BY SE3.E3_NUM, SE3.E3_PREFIXO, SE3.E3_PARCELA, SE3.E3_TIPO, SE3.E3_EMISSAO "

								cQrySE3 := ChangeQuery(cQrySE3)

								TCQUERY cQrySE3 NEW ALIAS (cAlsSE3)

								While (cAlsSE3)->(!Eof())

									cQryZPX := "SELECT * FROM " + RetSqlName("ZPX") + " ZPX "
									cQryZPX += "LEFT JOIN " + RetSqlName("SB1") + " SB1  ON "
									cQryZPX += "ZPX.ZPX_FILIAL = '"+xFilial("ZPX")+"' AND "
									cQryZPX += "SB1.B1_FILIAL = '"+xFilial("SB1")+"' AND "
									cQryZPX += "ZPX.ZPX_CODPRO = SB1.B1_COD AND "
									cQryZPX += "SB1.B1_XLICADQ IN " + FormatIn(cProdBema,"/") + " "
									cQryZPX += "WHERE "
									cQryZPX += "ZPX.ZPX_DOC    = '" + (cAlsSE3)->E3_NUM             +"' AND "
									cQryZPX += "ZPX.ZPX_SERIE  = '" + (cAlsSE3)->E3_PREFIXO         +"' AND "
									cQryZPX += "ZPX.ZPX_PARCEL = '" + (cAlsSE3)->E3_PARCELA         +"' AND "
									cQryZPX += "ZPX.ZPX_TIPO   = '" + (cAlsSE3)->E3_TIPO            +"' AND "
									cQryZPX += "ZPX.ZPX_REFER  = '" + (cAlsSE3)->E3_EMISSAO         +"' AND "
									cQryZPX += "ZPX.ZPX_UUID  <> '' AND "
									cQryZPX += "ZPX.D_E_L_E_T_ = '' "

									cQryZPX := ChangeQuery(cQryZPX)
									cAlsZPX := GetNextAlias()

									TCQUERY cQryZPX NEW ALIAS (cAlsZPX)

									While (cAlsZPX)->(!Eof())

										cJson := ' { '
										cJson += ' "IDExterno":"'   + (cAlsZPX)->ZPX_UUID +'",'
										cJson += ' "Status":"'      + cStatus +'",'
										cJson += ' } '

										//REST - TOKEN
										lInteg := U_TGtTknAqds( cCodInt , cProcesso , @cToKenExt )

										//---------------------------------
										//Consome o WS
										//---------------------------------
										lInteg := U_FCsmWSAdq( cJson , cCodInt, cProcesso , /*cComplURL*/ , @cMsgVld , cToKenExt , @cRetWsPad , @cRetWsErro , /*aHeadAPI*/ )

										//---------------------------------
										//Processa o retorno do WS
										//---------------------------------
										lInteg := U_TRspOracle( lInteg , cRetWsPad , cRetWsErro , cMsgVld , /*aRetProc*/ , aRecPN1 )

										//Se integrou atualizo
										If lInteg

											//Gera Registro PN1
											U_TSeekPn1( "E", "2", cCodInt, "ZPX", (cAlsZPX)->ZPX_IDSEQ, FwxFilial("ZPX"), MsDate() , Time() , MsDate() , Time() , "", "", cRetWsErro, "1" , (cAlsZPX)->R_E_C_N_O_ , "2", "ZPX_IDSEQ"  , .F. )

											//Atualiza ZPX_MSEXP
											//FUpdZPX((cAliasX)->ZPX_IDSEQ, cIdUUID )

										EndIf

										(cAlsZPX)->(DbSkip())

									End

									(cAlsSE3)->(DbSkip())

								End

								(cAlsSE3)->(DbCloseArea())

							EndIf

						EndIf

					EndIf

					SD1->(DbSkip())

				End

			EndIf

		EndIf

	EndIf

// INTEGRACAO PSA SERVICOS
	If lHbIntPSA .AND. findFunction("u_inJobPSA") .And. nConfirma == 1

		// posiciona no registro da SF1 que entrou no ponto de entrada, para garantir que as rotinas acima nao desposicionaram a tabela
		RestArea(aSF1Area)

		//NCC - NOTA DE CANCELAMENTO
		If SF1->F1_TIPO == "D" .AND. SF1->(FieldPos("F1_XIDPSA")) > 0 .AND. !Empty(SF1->F1_XIDPSA)

			//CLASSIFICACAO DA NF
			If nOpcao == 4
				aadd(aDadosPSA, '1'            ) // 1 - Tipo de Processamento
				aadd(aDadosPSA, SF1->F1_XIDPSA ) // 2 - ctm_nccid
				aadd(aDadosPSA, SF1->F1_DOC    ) // 3 - ctm_numeronfgerada
				aadd(aDadosPSA, SF1->F1_SERIE  ) // 4 - ctm_serienfgerada
				aadd(aDadosPSA, SF1->(recno()) ) // 5 - RECNO do Registro
				aadd(aDadosPSA, __cUserID      ) // 6 - Usuario do Sistema
				aadd(aDadosPSA, ""             ) // 7 - Mensagem
				aadd(aDadosPSA, Date()         ) // 8 - Data da reclassificacao

				StartJob( "u_inJobPSA", GetEnvServer(), .F., SM0->M0_CODIGO, SM0->M0_CODFIL, {'SF1'}, aDadosPSA, 'PutNcc')

				//EXCLUSAO DA NF
			ElseIf nOpcao == 5 .OR. nOpcao == 20 .OR. nOpcao == 21
				aadd(aDadosPSA, '2'            ) // 1 - Tipo de Processamento
				aadd(aDadosPSA, SF1->F1_XIDPSA ) // 2 - ctm_nccid
				aadd(aDadosPSA, ""             ) // 3 - ctm_numeronfgerada
				aadd(aDadosPSA, ""             ) // 4 - ctm_serienfgerada
				aadd(aDadosPSA, SF1->(recno()) ) // 5 - RECNO do Registro
				aadd(aDadosPSA, __cUserID      ) // 6 - Usuario do Sistema
				aadd(aDadosPSA, ""             ) // 7 - Mensagem
				aadd(aDadosPSA, Ctod("")       ) // 8 - Data da reclassificacao

				StartJob( "u_inJobPSA", GetEnvServer(), .F., SM0->M0_CODIGO, SM0->M0_CODFIL, {'SF1'}, aDadosPSA, 'DeleteNCC')
			EndIf

			// NOTA FISCAL DE ENTRADA (TERCEIROS)
		elseif SF1->F1_TIPO == 'N' .and. nOpcao == 3

			//-- faz query para buscar o numero da medicao no pedido de compra relacionado a nota
			cAliasQry := getNextAlias()

			BeginSQL Alias cAliasQry
            SELECT SC7.C7_FILIAL, SC7.C7_NUM, SC7.C7_MEDICAO, SC7.C7_CONTRA, SC7.C7_CONTREV, SC7.C7_FORNECE, SC7.C7_LOJA, CND_XNRPSA
            FROM %table:SF1% SF1
            INNER JOIN %table:SD1% SD1
				ON SD1.%notDel% 
				AND SD1.D1_FILIAL  = SF1.F1_FILIAL
				AND SD1.D1_DOC     = SF1.F1_DOC
				AND SD1.D1_SERIE   = SF1.F1_SERIE
				AND SD1.D1_FORNECE = SF1.F1_FORNECE
				AND SD1.D1_LOJA    = SF1.F1_LOJA
            INNER JOIN %table:SC7% SC7
				ON SC7.%notDel% 
				AND SC7.C7_FILIAL  = SD1.D1_FILIAL   
				AND SC7.C7_NUM     = SD1.D1_PEDIDO   
				AND SC7.C7_ITEM    = SD1.D1_ITEMPC   
				AND SC7.C7_FORNECE = SD1.D1_FORNECE  
               	AND SC7.C7_LOJA    = SD1.D1_LOJA     
               	AND SC7.C7_MEDICAO <> ' '
			INNER JOIN %table:CND% CND    
				ON  CND_FILIAL = SC7.C7_FILIAL    
				AND CND_NUMMED = SC7.C7_MEDICAO    
				AND CND_CONTRA = SC7.C7_CONTRA 
            WHERE SF1.%notDel% 
				AND SF1.F1_FILIAL  = %exp:SF1->F1_FILIAL%
				AND SF1.F1_TIPO    = %exp:SF1->F1_TIPO%
				AND SF1.F1_DOC     = %exp:SF1->F1_DOC%
				AND SF1.F1_SERIE   = %exp:SF1->F1_SERIE%
				AND SF1.F1_FORNECE = %exp:SF1->F1_FORNECE%
				AND SF1.F1_LOJA    = %exp:SF1->F1_LOJA%
            FETCH FIRST 1 ROWS ONLY
			EndSQL

			cNumMed := ""

			If ! (cAliasQry)->(Eof())
				cNumMed   := (cAliasQry)->C7_MEDICAO
				cNumCompo := (cAliasQry)->CND_XNRPSA
			EndIf

			(cAliasQry)->(dbCloseArea())

			//-- se nao retornar o numero da medicao nao precisa atualizar os dados no PSA
			If ! Empty(cNumMed)

				//-----------------------------------------------
				// aDados[1]  // dados
				//      aDados[1,1]  // operacao => 3=inclusao, 5=exclusao
				//      aDados[1,2]  // tipo => 1=Atualiza numero da medicao, 2=Atualiza numero do pedido, 3=atualiza numero da nota
				//      aDados[1,3]  // id do fechamento
				//      aDados[1,4]  // numero da medicao
				//      aDados[1,5]  // numero do pedido de compra
				//      aDados[1,6]  // numero da nota fiscal
				//      aDados[1,7]  // serie da nota fiscal
				//      aDados[1,8]  // texto log
				//      aDados[1,9]  // composição
				// aDados[2]  // numero do registro
				// aDados[3]  // usuario
				// aDados[4]  // codigo PEM
				//-----------------------------------------------

				//-- Preenche dados referente ao ponto de entrada
				aRet := {}

				AAdd(aRet, nOpcao )         // 1 - operacao
				AAdd(aRet, "3" )            // 2 - tipo (3=atualiza numero da nota)
				AAdd(aRet, "" )             // 3 - id do fechamento
				AAdd(aRet, cNumMed )        // 4 - numero da medicao
				AAdd(aRet, "" )             // 5 - numero do pedido de compra
				AAdd(aRet, SF1->F1_DOC )    // 6 - numero da nota fiscal
				AAdd(aRet, SF1->F1_SERIE )  // 7 - serie da nota fiscal
				AAdd(aRet, "" )             // 8 - texto log
				AAdd(aRet, cNumCompo)       // 9 - composição

				//-- Preenche dados a serem enviados
				AAdd(aDadosPSA, aRet )
				AAdd(aDadosPSA, SF1->(Recno()) )
				AAdd(aDadosPSA, __cUserID )

				//-- chama o metodo para atualizar dados no PSA
				StartJob("u_inJobPSA",GetEnvServer(),.F.,cEmpAnt,cFilAnt,{'SC7','SF1','SD1','CND'},aDadosPSA,"PutStMed")

			EndIf

		EndIf
	EndIf
	//Funcao responsavel por verificar se a nota excluida é financiada na Supplier, se sim é enviado um email para  os destinatarios do parametro abaixo
	If  nConfirma == 1 .And. (nOpcao == 3 .Or. nOpcao == 5)
		IF SF1->F1_TIPO ='D'//Somente se for NF devolucao (retornar)
			U_GetNFSup(SF1->F1_FILIAL, cDocS , cSerieS ,SF1->F1_FORNECE, SF1->F1_LOJA,nOpcao )
		EndIF
		
		If nOpcao == 5 .And. SF1->F1_TIPO == "D" // Na exclusão, exclui da PB1 também
			
			cSeek := SD1->D1_FILIAL+"2"+SD1->(D1_DOC+D1_SERIE+D1_FORNECE+D1_LOJA+D1_COD+PADR(D1_ITEM,GetSx3Cache("PB1_ITEM","X3_TAMANHO")))
			PB1->(dbSetOrder(1))//PB1_FILIAL+PB1_ORIGEM+PB1_DOC+PB1_SERIE+PB1_CLIENT+PB1_LOJA+PB1_PRODUT+PB1_ITEM
			
			If PB1->(DbSeek(cSeek))
				RecLock("PB1",.F.)
					PB1->(DbDelete())
				PB1->(MsUnlock())
			EndIf

			PB1->(RestArea(aAreaPb1))
			aSize(aAreaPb1,0)

		EndIF
	EndIF


	// Gravando valores nas linhas de impostos de acordo com o csv
	If FwIsInCallStack('U_TCOMA011') .and. nOpcao == 3
	   nPosDeduz := aScan(aAutoCab   , {|x| AllTrim(x[1]) = "DEDUZ"      } )		
       lDeduz    := Iif(nPosDeduz > 0, !aAutoCab[nPosDeduz,2], .F.)
	   lDifere   := !lDeduz
	   GravaImpCSV(lDeduz, aAutoCab)
	EndIf
	
	//Verifica se a NF é despesa Diferida Antecipada
	IF(lDespDif .And.   nConfirma == 1)
		oTIContab:= TICONTABIL():New()
		//Exclusao da NF
		if( nOpcao == 5 .OR. nOpcao == 20 .OR. nOpcao == 21)
			oTIContab:deleteDespesaDiferida(SF1->F1_FILIAL,SF1->F1_TIPO,SF1->F1_DOC,SF1->F1_SERIE,SF1->F1_FORNECE,SF1->F1_LOJA)
		//CLASSIFICACAO DA NF ou INCLUSAO Quando Realmente vai para o financeiro
		ElseIF (nOpcao == 4 .Or. nOpcao == 3 )			
			oTIContab:verifyDespesaDiferida(SF1->F1_FILIAL,SF1->F1_TIPO,SF1->F1_DOC,SF1->F1_SERIE,SF1->F1_FORNECE,SF1->F1_LOJA,,,,,,lDifere)			
		EndIF
		FREEOBJ( oTIContab )
	EndIF

	RestArea(aArea)
	RestArea(aSF1Area)
	RestArea(aSD1Area)
	RestArea(aSC7Area)
	RestArea(aSE3Area)
	RestArea(aZPXArea)
	

Return











//-------------------------------------------------------------------
/*/{Protheus.doc} GravaImpCSV
Gravando SE2 de acordo com o csv importado
<AUTHOR>
@since   03-07-2024
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function GravaImpCSV(lDeduz, aAutoCab)
Local aSD1Area  := SD1->(GetArea())
Local aSE2Area  := SE2->(GetArea())
Local cChave    := SF1->F1_FILIAL + SF1->F1_DOC + SF1->F1_SERIE + SF1->F1_FORNECE + SF1->F1_LOJA
Local cTitPai   := SE2->(E2_FILIAL + E2_PREFIXO + E2_NUM + E2_PARCELA + E2_TIPO + E2_FORNECE + E2_LOJA)
Local cSeq      := '000'
Local cAuxHist  := ""
Local cNatImpPIS:= GetMv("TI_NIMPPIS", , "0110014")
Local cNatImpCOF:= GetMv("TI_NIMPCOF", , "0110013")
Local cNatImpCID:= GetMv("TI_NIMPCID", , "0110015")
Local cNatImpIR := GetMv("TI_NIMPIR" , , "0110012")
Local nImpDed   := GetMv("TI_IMPDEDU", , SF1->F1_IRRF + SF1->F1_ISS)
Local nVlNota   := Iif(lDeduz, SF1->F1_VALBRUT - nImpDed, SF1->F1_VALBRUT)
Local lProsseg  := .T.
Local dVecto    := ctod('')
Local nPosVcto  := aScan(aAutoCab   , {|x| AllTrim(x[1]) = "E2_VENCTO"  } )	
Local nPosNro   := aScan(aAutoCab   , {|x| AllTrim(x[1]) = "F1_DOC"  } )	
Local lValDiaUt := GetMv("TI_MT10301",,.T.)

If nPosNro <= 0 .or. Alltrim(aAutoCab[nPosNro,2]) != Alltrim(SF1->F1_DOC)
	lProsseg := .F. //quando ocorre erro no execauto de NF já cadastrada SF1 não é posicionado
Else 
	If SE2->E2_FILIAL != SF1->F1_FILIAL .or. SE2->E2_PREFIXO != SF1->F1_SERIE  .or. SE2->E2_NUM != SF1->F1_DOC .or. SE2->E2_FORNECE != SF1->F1_FORNECE
		lProsseg := .F.
		SE2->(dbSetOrder(6))    // E2_FILIAL+E2_FORNECE+E2_LOJA+E2_PREFIXO+E2_NUM+E2_PARCELA+E2_TIPO
		SE2->(dbSeek(SF1->F1_FILIAL+SF1->F1_FORNECE+SF1->F1_LOJA+SF1->F1_PREFIXO+SF1->F1_DOC+"   "+"NF"))//Vrf SE2 e todos tit pai tinham parc='   ' 
		If SE2->(!Eof())
			cTitPai   := SE2->(E2_FILIAL + E2_PREFIXO + E2_NUM + E2_PARCELA + E2_TIPO + E2_FORNECE + E2_LOJA)
			dVecto    := Iif(nPosVcto > 0 , aAutoCab[nPosVcto,2], SE2->E2_VENCTO)
			lProsseg := .T.
		EndIf 
	Else 
		dVecto    := Iif(nPosVcto > 0 , aAutoCab[nPosVcto,2], SE2->E2_VENCTO)
	Endif 
EndIf 

If lProsseg
	SD1->(dbSetOrder(1))
	SD1->(dbSeek(cChave))
 
	// Titulo principal
	SE2->(Reclock("SE2",.F.))
	SE2->E2_VENCTO  := dVecto
	SE2->E2_VENCORI := dVecto
	SE2->E2_VENCREA := dVecto
	
	SE2->E2_BASEIRF := SF1->F1_VALMERC
	SE2->E2_IRRF    := SF1->F1_IRRF
	SE2->E2_VRETIRF := SF1->F1_VALIRF
	SE2->E2_CIDE    := SF1->F1_VLCIDE
	
	SE2->E2_VRETPIS := SF1->F1_VALIMP6
	SE2->E2_VRETCOF := SF1->F1_VALIMP5
	SE2->E2_ISS     := SF1->F1_ISS
	
	SE2->E2_VALOR   := nVlNota
	SE2->E2_SALDO   := nVlNota
	SE2->E2_VLCRUZ  := nVlNota

	SE2->(MsUnlock())

	cAuxPref := SE2->E2_PREFIXO	
		
	SE2->(dbSetOrder(17)) 
	SE2->(MsSeek(cTitPai))
	// impostos
	While !SE2->(EOF()) .And. cTitPai == SE2->(E2_FILIAL + AllTrim(E2_TITPAI) )

		cAuxHist := ""
		cSeq     := Soma1(cSeq)
						
		SE2->(Reclock("SE2",.F.))
				
		Do Case 
		   Case SE2->E2_TIPO = "TX"			  	 
		   		SE2->E2_VALOR   := SF1->F1_IRRF
		   		SE2->E2_SALDO   := SF1->F1_IRRF
		   		SE2->E2_VLCRUZ  := SF1->F1_IRRF		
				SE2->E2_VENCTO  := dVecto
				SE2->E2_VENCORI := dVecto
				SE2->E2_VENCREA := dVecto
				SE2->E2_NATUREZ := cNatImpIR 					
		   
		   Case SE2->E2_TIPO = "CID"
		   		// Calcula o vencimento do cid
				// dia 15 do mes subsequente do vencimento validando se é dia util
			    If lValDiaUt
					// Busca o dia util mais proximo do dia 15 do mes subsequente
					nDiaCide := Day(SE2->E2_VENCTO)
					dDataUtil := DataValida(cTod(cvaltochar(nDiaCide) + Right(dtoc(MonthSum(dVecto, 1)),8)), .F.)
					While Day(dDataUtil) < 15 .And. nDiaCide < 15 // Garante que avalia somente até o dia 15
						dDataUtil := DataValida(cTod(cvaltochar(++nDiaCide) + Right(dtoc(MonthSum(dVecto, 1)),8)),.F.)
					EndDo
				Else
					// Busca o dia util da 1a quinzena do mes subsequente
					nDiaCide := Day(SE2->E2_VENCTO)
					dDataVenc:= cTod(cvaltochar(nDiaCide) + Right(dtoc(MonthSum(dVecto, 1)),8))
					dDataUtil:= DataValida(dDataVenc ,.F.)	
				EndIf

  		        SE2->E2_VALOR := SF1->F1_VLCIDE
			    SE2->E2_SALDO := SF1->F1_VLCIDE
			    SE2->E2_VLCRUZ:= SF1->F1_VLCIDE				
				SE2->E2_NATUREZ := cNatImpCID 
				SE2->E2_VENCTO  := dDataUtil			
				SE2->E2_VENCORI := dDataUtil
				SE2->E2_VENCREA := dDataUtil

		   Case SE2->E2_TIPO = "ISS"
		   		// Calcula o vencimento do ISS
				// dia 15 do mes subsequente do vencimento validando se é dia util
				nDiaISS  := Day(SE2->E2_VENCTO)
				dDataVenc:= cTod(cvaltochar(nDiaISS) + Right(dtoc(MonthSum(dVecto, 1)),8))
				dDataUtil:= DataValida(dDataVenc ,.F.)	
				SE2->E2_VENCTO  := dDataUtil			
				SE2->E2_VENCORI := dDataUtil
				SE2->E2_VENCREA := dDataUtil
				SE2->E2_VALOR   := SF1->F1_ISS
			    SE2->E2_SALDO   := SF1->F1_ISS
			    SE2->E2_VLCRUZ  := SF1->F1_ISS				
		
		   Case SE2->E2_TIPO = "PIS"
		   		SE2->E2_NATUREZ := cNatImpPIS 
				SE2->E2_VENCTO  := dVecto
				SE2->E2_VENCORI := dVecto
				SE2->E2_VENCREA := dVecto
				SE2->E2_PREFIXO := SF1->F1_SERIE

		   Case SE2->E2_TIPO = "COF"
		   		SE2->E2_NATUREZ := cNatImpCOF 
				SE2->E2_VENCTO  := dVecto
				SE2->E2_VENCORI := dVecto
				SE2->E2_VENCREA := dVecto
				SE2->E2_PREFIXO := SF1->F1_SERIE

	   	EndCase

		If SF1->F1_EST == "EX"
		   SE2->E2_PARCELA   :=   cSeq
		EndIf
		SE2->E2_CCUSTO    :=   SD1->D1_CC
		SE2->E2_ITEMCTA   :=   SD1->D1_ITEMCTA
		SE2->E2_CLVL      :=   SD1->D1_CLVL

	    cCompet      := RIGHT(MesANO(SE2->E2_VENCTO),2)+LEFT(MesANO(SE2->E2_VENCTO),4)
        SE2->E2_HIST := ALLTRIM(SE2->E2_HIST) + " " + Iif(SE2->E2_TIPO="TX","IR",SE2->E2_TIPO)+ " " + cCompet

		SE2->E2_TIPO := "TX"
			
		SE2->(MsUnlock())

  		SE2->(DbSkip())

	EndDo	
Endif 

RestArea(aSD1Area)
RestArea(aSE2Area)

Return
